#main-sidebar-wrapper button[data-sidebar="menu-button"]:hover,
#main-topbar-wrapper button:hover:not(.tab-close-btn) {
  background-color: hsl(var(--sidebar-accent));
}

#main-topbar-wrapper {
  border-bottom: 1px solid hsl(var(--sidebar-border));
}

[role="tab"] {
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
  position: relative;
  font-size: 0.9rem;
  border-bottom: 2px solid transparent;
}

[role="tab"]:hover {
  background-color: hsl(var(--muted) / 0.5);
}

[role="tab"][data-state="active"] {
  border-bottom-color: hsl(var(--primary));
  font-weight: 500;
}

.tab-close-btn {
  opacity: 0.6;
  transition: all 0.2s ease;
}

.tab-close-btn:hover {
  opacity: 1;
  background-color: hsl(var(--muted));
}

.chart-container {
  max-height: 300px;
  min-height: 200px;
}

#chart-price-history {
  height: 300px;
  width: 100%;
}

@media (max-width: 768px) {
  .chart-container {
    max-width: 100vw;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
  }
}

@media (max-width: 640px) {
  #main-sidebar-wrapper {
    display: none;
  }
  .chart-container {
    max-width: 100vw;
    min-width: 0;
    width: 100%;
    height: auto;
    min-height: 180px;
    max-height: 220px;
  }
  #stockChart {
    width: 100vw !important;
    min-width: 0 !important;
    height: 220px !important;
    max-height: 220px !important;
  }
  #allIndicators .chart-container {
    min-height: 120px;
    max-height: 160px;
    height: 140px;
  }
  #allIndicators canvas {
    height: 120px !important;
    max-height: 140px !important;
  }
}
