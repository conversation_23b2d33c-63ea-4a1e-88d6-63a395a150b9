const ANALYSIS_PATH = 'analysis/';
const MAIN_SUFFIX = '.json';
const INDICATOR_LABELS = {
    rsi: 'RSI',
    macd: 'MACD',
    ma: 'Moving Average',
    adx: 'ADX',
    cci: 'CCI',
    bb: 'Bollinger Bands',
    obv: 'OBV',
    roc: 'ROC',
    ultosc: 'Ultimate Oscillator',
    sar: 'Parabolic SAR',
    momentum: 'Momentum',
    ao: 'Awesome Oscillator',
    bearpower: 'Bear Power',
    stochastic: 'Stochastic',
    stochrsi: 'StochRSI',
    ichimoku: 'Ichimoku',
    pp: 'Pivot Points',
    tsf: 'TSF',
    adi: 'ADI',
    wpr: 'Williams %R',
    rsw: 'RS(52W)'
};

const chart1Color = 'hsl(173, 58%, 39%)'
const chart2Color = 'hsl(12, 76%, 61%)'

// Export priceChart instance to be managed by script.js if needed
export let priceChartInstance = null;

const mainDataCache = {};
const priceHistoryCache = {};
const indicatorDataCache = {}; // { symbol: { indicator: data } }

function fetchJSONWithCache(path, cache) {
    if (cache[path]) {
        return Promise.resolve(cache[path]);
    }
    return fetch(path)
        .then(res => res.json())
        .then(data => {
            cache[path] = data;
            return data;
        });
}

export function clearAllIndicatorsGlobally() {
    // Destroy all indicator chart instances
    for (const key in indicatorCharts) {
        if (indicatorCharts[key]) {
            indicatorCharts[key].destroy();
            delete indicatorCharts[key];
        }
    }
    const container = document.getElementById('allIndicators');
    if (container) container.innerHTML = '';
}

export function clearStockViewContent() {
    if (priceChartInstance) {
        priceChartInstance.remove();
        priceChartInstance = null;
    }
    const summaryDiv = document.getElementById('analysisSummary');
    if (summaryDiv) summaryDiv.innerHTML = '';
    clearAllIndicatorsGlobally();
}

export async function loadAndRenderSymbol(symbol) {
    if (!symbol || symbol === 'Watchlist') { // Guard against invalid symbol or "Watchlist"
        clearStockViewContent(); // Clear view if no valid symbol
        return;
    }
    const mainPath = ANALYSIS_PATH + symbol + MAIN_SUFFIX;
    const priceHistoryPath = ANALYSIS_PATH + symbol + '_prices.json';

    const mainData = await fetchJSONWithCache(mainPath, mainDataCache);
    renderAnalysisSummary(mainData);

    let priceHistory = null;
    try {
        priceHistory = await fetchJSONWithCache(priceHistoryPath, priceHistoryCache);
    } catch (e) {
        priceHistory = null;
    }
    renderStockChartFromHistory(priceHistory);
    renderAllIndicators(symbol);
}

function renderAnalysisSummary(data) {
    const div = document.getElementById('analysisSummary');
    div.innerHTML = `
        <div class="bg-white rounded shadow p-4">
            <div class="flex items-center space-x-6 mb-2">
                <span class="text-2xl font-bold">${data.s}</span>
                <span class="text-xl">${data.p.c.toLocaleString()} <span class="text-sm">(${data.p.cv}/${data.p.cp}%)</span></span>
                <span class="text-gray-500">${data.t.d} (${data.t.s})</span>
            </div>
            <div class="mb-2">${data.r.s.replace(/\n/g, '<br>')}</div>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                ${data.ma.map(ma => `<div class="border rounded p-2"><b>${ma.t} ${ma.p}</b>: ${ma.v} <span class="text-xs">[${ma.s}]</span></div>`).join('')}
            </div>
        </div>
    `;
}

/**
 * Render the stock price and volume chart using Lightweight Charts.
 * @param {Object} priceHistory - The price history object with t, o, h, l, c, v arrays.
 */
function renderStockChartFromHistory(priceHistory) {
    if (!priceHistory || !priceHistory.c || priceHistory.c.length === 0) {
        const container = document.getElementById('chart-price-history');
        container.innerHTML = '<div style="text-align:center;color:#888;padding:100px 0;">No price history data available</div>';
        return;
    }
    // Prepare data for Lightweight Charts
    const container = document.getElementById('chart-price-history');
    container.innerHTML = '';
    if (priceChartInstance) {
        priceChartInstance.remove();
        priceChartInstance = null;
    }

    priceChartInstance = LightweightCharts.createChart(container, {
        autoSize: true,
        width: container.clientWidth || 900,
        height: container.clientHeight || 300,
        layout: {
            backgroundColor: "#ffffff",
            textColor: "#333",
            attributionLogo: false,
            panes: {
                separatorColor: "#F3F4F6",
                separatorHoverColor: "#F3F4F6",
                enableResize: true,
            },
        },
        grid: {
            vertLines: { color: "#FBFBFC" },
            horzLines: { color: "#FBFBFC" },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: "#F3F4F6",
            scaleMargins: {
                top: 0.3, // leave some space for the legend
                bottom: 0.25,
            },
        },
        timeScale: {
            borderColor: "#F3F4F6",
            timeVisible: true,
            secondsVisible: false,
        },
    });

    // Candlestick data
    const candlestickSeries = priceChartInstance.addSeries(
        LightweightCharts.CandlestickSeries,
        {
            upColor: "hsl(173, 58%, 39%)",
            downColor: "hsl(12,76%, 61%)",
            borderVisible: false,
            wickUpColor: "hsl(173, 58%, 39%)",
            wickDownColor: "hsl(12,76%, 61%)",
        }
    );
    const candleData = priceHistory.c.map((close, i) => ({
        time: priceHistory.t[i],
        open: priceHistory.o[i],
        high: priceHistory.h[i],
        low: priceHistory.l[i],
        close: close,
    }));

    candlestickSeries.setData(candleData);

    // Volume data
    const volumeSeries = priceChartInstance.addSeries(
        LightweightCharts.HistogramSeries,
        {
            priceFormat: {
                type: "volume"
            }
        },
        1
    );
    const volumeData = priceHistory.v.map((v, i) => ({
        time: priceHistory.t[i],
        value: v,
        color: candleData[i].close > candleData[i].open ? 'hsl(173, 58%, 39%)' : 'hsl(12, 76%, 61%)',
    }));
    volumeSeries.setData(volumeData);
    volumeSeries.priceScale().applyOptions({
        autoScale: true,
        scaleMargins: {
            top: 0.1, // highest point of the series will be 70% away from the top
            bottom: 0,
        },
    });
}


const indicatorCharts = {};

function getColorForMA(key) {
    const colors = ['#1976d2', '#388e3c', '#fbc02d', '#d32f2f', '#7b1fa2', '#0288d1', '#c2185b', '#ff9800'];
    // s5, e5, s10, e10, ...
    const num = parseInt(key.match(/\d+/)?.[0] || '0');
    return colors[num % colors.length];
}
function getColorForIchimoku(key) {
    const colorMap = {
        tk: '#1976d2', // Tenkan
        kj: '#388e3c', // Kijun
        sa: '#fbc02d', // Senkou A
        sb: '#d32f2f', // Senkou B
        cs: '#7b1fa2', // Chikou
    };
    return colorMap[key] || '#0288d1';
}
function getColorForPP(key) {
    const colorMap = {
        pivot: '#1976d2',
        r1: '#388e3c',
        r2: '#fbc02d',
        r3: '#0288d1',
        s1: '#d32f2f',
        s2: '#7b1fa2',
        s3: '#c2185b',
    };
    return colorMap[key] || '#888';
}

async function renderAllIndicators(symbol) {
    clearAllIndicatorsGlobally();
    const container = document.getElementById('allIndicators');
    const isMobile = window.innerWidth <= 640;
    if (!indicatorDataCache[symbol]) indicatorDataCache[symbol] = {};
    for (const key of Object.keys(INDICATOR_LABELS)) {
        const indicatorPath = ANALYSIS_PATH + symbol + '_' + key + MAIN_SUFFIX;
        try {
            let indicatorData;
            if (indicatorDataCache[symbol][key]) {
                indicatorData = indicatorDataCache[symbol][key];
            } else {
                indicatorData = await fetchJSONWithCache(indicatorPath, {});
                indicatorDataCache[symbol][key] = indicatorData;
            }
            // Create a card for each indicator
            const card = document.createElement('div');
            card.className = 'chart-container bg-white rounded shadow flex flex-col mb-12';
            const title = document.createElement('div');
            title.className = 'font-semibold mb-2 text-center';
            title.textContent = (INDICATOR_LABELS[key] || key) + ' (Weekly)';
            const canvas = document.createElement('canvas');
            // Set canvas size for indicator charts (not close price)
            if (isMobile) {
                canvas.style.width = '100%';
                canvas.style.height = '300px';
            } else {
                canvas.style.width = '100%';
                canvas.style.height = '300px';
            }
            card.appendChild(title);
            card.appendChild(canvas);
            container.appendChild(card);
            // Render chart
            renderIndicatorChartToCanvas(key, indicatorData, canvas, key);
        } catch (e) {
            // No data for this indicator, skip
        }
    }
}

function renderIndicatorChartToCanvas(indicator, data, canvas, chartKey) {
    if (indicatorCharts[chartKey]) {
        indicatorCharts[chartKey].destroy();
    }
    let labels = [];
    let datasets = [];
    if (data.t && Array.isArray(data.t)) {
        labels = data.t.map(ts => new Date(ts * 1000).toLocaleDateString());
    }
    if (indicator === 'ma') {
        // Render all s* and e* keys as separate lines
        Object.keys(data).forEach(key => {
            if ((/^s\d+$/i.test(key) || /^e\d+$/i.test(key)) && Array.isArray(data[key])) {
                datasets.push({
                    label: key.toUpperCase(),
                    data: data[key],
                    borderColor: getColorForMA(key),
                    backgroundColor: 'rgba(0,0,0,0)',
                    fill: false,
                    tension: 0.1,
                });
            }
        });
    } else if (indicator === 'ichimoku') {
        const ichimokuLines = {
            tk: 'Tenkan',
            kj: 'Kijun',
            sa: 'Senkou A',
            sb: 'Senkou B',
            cs: 'Chikou'
        };
        Object.entries(ichimokuLines).forEach(([key, label]) => {
            if (data[key]) {
                datasets.push({
                    label,
                    data: data[key],
                    borderColor: getColorForIchimoku(key),
                    backgroundColor: 'rgba(0,0,0,0)',
                    fill: false,
                    tension: 0.1,
                });
            }
        });
    } else if (indicator === 'pp') {
        // Render all pivot, r1, r2, r3, s1, s2, s3
        ['pivot', 'r1', 'r2', 'r3', 's1', 's2', 's3'].forEach(key => {
            if (data[key]) {
                datasets.push({
                    label: key.toUpperCase(),
                    data: data[key],
                    borderColor: getColorForPP(key),
                    backgroundColor: 'rgba(0,0,0,0)',
                    fill: false,
                    tension: 0.1,
                });
            }
        });
    } else {
        // Default: v, k, d, m, u, l
        if (data.v) {
            datasets.push({
                label: INDICATOR_LABELS[indicator] || indicator,
                data: data.v,
                borderColor: 'rgba(75,192,192,1)',
                backgroundColor: 'rgba(75,192,192,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.k) {
            datasets.push({
                label: 'K',
                data: data.k,
                borderColor: 'rgba(255,99,132,1)',
                backgroundColor: 'rgba(255,99,132,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.d) {
            datasets.push({
                label: 'D',
                data: data.d,
                borderColor: 'rgba(54,162,235,1)',
                backgroundColor: 'rgba(54,162,235,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.m) {
            datasets.push({
                label: 'M',
                data: data.m,
                borderColor: 'rgba(255,206,86,1)',
                backgroundColor: 'rgba(255,206,86,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.u) {
            datasets.push({
                label: 'Upper',
                data: data.u,
                borderColor: 'rgba(153,102,255,1)',
                backgroundColor: 'rgba(153,102,255,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.l) {
            datasets.push({
                label: 'Lower',
                data: data.l,
                borderColor: 'rgba(255,159,64,1)',
                backgroundColor: 'rgba(255,159,64,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
    }
    indicatorCharts[chartKey] = new Chart(canvas.getContext('2d'), {
        type: 'line',
        data: { labels, datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' },
                title: { display: false },
            },
            scales: {
                y: { beginAtZero: false },
                x: { ticks: { maxTicksLimit: 8, autoSkip: true } }
            }
        }
    });
}
