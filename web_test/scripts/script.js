import { selectTab, addTab } from './tabs.js';
import { loadAndRenderSymbol, clearStockViewContent, priceChartInstance, clearAllIndicatorsGlobally } from './stock.js';
import { initWatchlist, displayWatchlist, hideWatchlist } from './watchlist.js';

const SYMBOLS = ['HPG', 'TCB', 'VNM']; // For static site, hardcode or generate this list

let currentSymbol = '';

// DOM Elements for view switching
let stockDetailViewEl;
let watchlistViewEl;

function renderSymbolList() {
    const tabList = document.getElementById('selectedSymbolsPlaceholder');

    let watchlistTab = addTab(tabList, 'Watchlist', false, {
        afterSelected: () => { // tabValue will be "Watchlist"
            currentSymbol = 'Watchlist'; // Special value to indicate watchlist view
            if (stockDetailViewEl) stockDetailViewEl.style.display = 'none';
            displayWatchlist();
            clearStockViewContent(); // Clear charts and summary from stock.js
        }
    });

    SYMBOLS.forEach((symbolValue) => {
        addTab(tabList, symbolValue, true, {
            afterSelected: (selectedSymbol) => { // selectedSymbol is the actual symbol string
                currentSymbol = selectedSymbol;
                hideWatchlist();
                if (stockDetailViewEl) stockDetailViewEl.style.display = 'block';
                loadAndRenderSymbol(currentSymbol);
            }
        });
    });

    selectTab(tabList, watchlistTab);
}

// Debounce utility to limit how often a function is called
function debounce(func, wait) {
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

document.addEventListener('DOMContentLoaded', function () {
    stockDetailViewEl = document.getElementById('stockDetailView');
    watchlistViewEl = document.getElementById('watchlistView');
    initWatchlist(watchlistViewEl); // Initialize the watchlist module with its container

    renderSymbolList();

    // After renderSymbolList, the last SYMBOLS tab will be active due to addTab calling selectTab.
    // We want the Watchlist tab to be active initially.
    // So, find the watchlistTab and select it.
    const tabList = document.getElementById('selectedSymbolsPlaceholder');
    const watchlistTabElement = Array.from(tabList.querySelectorAll('[role="tab"]'))
        .find(tab => tab.dataset.value === 'Watchlist');
    if (watchlistTabElement) {
        selectTab(tabList, watchlistTabElement); // This will trigger its afterSelected callback
    }
});

// Re-render all charts on window resize
window.addEventListener('resize', debounce(() => {
    if (currentSymbol && currentSymbol !== 'Watchlist') {
        loadAndRenderSymbol(currentSymbol); // Re-render only if a specific stock is active
    }
}, 200));
