import argparse
import json
import logging
import os
import re
import shutil
import subprocess
from datetime import datetime
from typing import Any, Dict, List, Optional

from analysis.indicator_utils import (
    calculate_indicators,
    get_last_trading_weekdays,
    get_prices_up_to,
)
from analysis.formatting_utils import (
    format_zones,
    risk_reward_processing,
    summary_processing,
)

from stockpal.core.sql_service import SqliteService
from stockpal.core.stock import PriceData
from stockpal.core.util import Utils
from stockpal.data.data_reader import DataReader
from stockpal.db import SymbolDao
from stockpal.indicator.adi import AccumulationDistributionIndex
from stockpal.indicator.adx import AverageDirectionalIndex
from stockpal.indicator.ao import AwesomeOscillator
from stockpal.indicator.bb import BollingerBands
from stockpal.indicator.bearpower import BearPower
from stockpal.indicator.cci import CommodityChannelIndex
from stockpal.indicator.ichimoku import Ichimoku
from stockpal.indicator.momentum import Momentum
from stockpal.indicator.obv import OnBalanceVolume
from stockpal.indicator.pivot_points import PivotPoints
from stockpal.indicator.roc import RateOfChange
from stockpal.indicator.sar import ParabolicSAR
from stockpal.indicator.stoch import StochasticOscillator
from stockpal.indicator.stochrsi import StochasticRSI
from stockpal.indicator.tsf import TimeSeriesForecast
from stockpal.indicator.ultosc import UltimateOscillator
from stockpal.indicator.wpr import WilliamsPercentR

# Configure logging for the module
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)

# Get web directory
web_dir = Utils.get_web_dir()
logging.debug(f"web_dir: {web_dir}")
web2_dir = Utils.get_web2_dir()
logging.debug(f"web2_dir: {web2_dir}")
# Get bidaskprice.github.io directory
bidaskprice_dir = Utils.get_bidaskprice_dir()
logging.debug(f"bidaskprice_dir: {bidaskprice_dir}")

is_deploy = False  # If True, perform post-processing (move files and push to GitHub)


def main(analysis_symbols: list[str] | None, analysis_date: str | None) -> None:
    """
    Main entry point for stock analysis and site generation workflow.
    Handles data fetching, analysis, file generation, and optional deployment to GitHub Pages repo.

    Indicator Export Logic:
    (a) Indicator values are calculated for each relevant date (e.g., last 5 Fridays),
        using only price data up to and including that date. This ensures that each
        exported value is aligned with its timestamp and that no future data is used
        (no lookahead bias).
    (b) If there is missing or insufficient data for a calculation (e.g., not enough
        price history for a moving average), the exported value for that date is None.
    (c) All exported arrays (timestamps and indicator values) are aligned by index,
        so that each value corresponds to the correct date.
    """

    sql_service = SqliteService()
    symbol_dao = SymbolDao(sql_service=sql_service)
    stocks = symbol_dao.get_all()
    symbols = [s.code for s in stocks]

    # Xác định ngày phân tích
    end_date = None
    if analysis_date:
        end_date = datetime.strptime(analysis_date, "%Y-%m-%d")

    if analysis_symbols and len(analysis_symbols) > 0:
        symbols = analysis_symbols

    for symbol in symbols:
        if symbol in ["VNINDEX"]:
            continue

        try:
            analyze_result = analyze_stock(symbol=symbol, end_date=end_date)

            # Save stock current info
            save_stock_current_info(analyze_result)

            indicator_arrays = analyze_result.get("indicators", {})
            indicator_map = {
                "ma": "symbol_ma.json",
                "macd": "symbol_macd.json",
                "rsi": "symbol_rsi.json",
                "rsw": "symbol_rs_52w.json",
            }
            # Save all indicator arrays that are present
            for ind, template in indicator_map.items():
                if ind in indicator_arrays:
                    save_indicator_span(
                        symbol, ind, indicator_arrays[ind], None
                    )  # See template: server/templates/{template}

            # List of all templates/indicators
            all_templates = [
                ("adi", "symbol_adi.json"),
                ("adx", "symbol_adx.json"),
                ("ao", "symbol_ao.json"),
                ("bb", "symbol_bb.json"),
                ("bearpower", "symbol_bearpower.json"),
                ("cci", "symbol_cci.json"),
                ("ichimoku", "symbol_ichimoku.json"),
                # ("ma", "symbol_ma.json"),
                # ("macd", "symbol_macd.json"),
                ("momentum", "symbol_momentum.json"),
                ("obv", "symbol_obv.json"),
                ("pp", "symbol_pp.json"),
                ("roc", "symbol_roc.json"),
                # ("rsw", "symbol_rs_52w.json"),
                # ("rsi", "symbol_rsi.json"),
                ("sar", "symbol_sar.json"),
                ("stochastic", "symbol_stochastic.json"),
                ("stochrsi", "symbol_stochrsi.json"),
                ("tsf", "symbol_tsf.json"),
                ("ultosc", "symbol_ultosc.json"),
                ("wpr", "symbol_wpr.json"),
            ]
            for ind, template in all_templates:
                try:
                    prices = DataReader(symbol).get_daily_prices(days=365)
                    # Find last 5 Fridays (trading days)
                    last5_fridays = get_last_trading_weekdays(prices, 5)
                    arr = {}
                    if ind == "bb":
                        bb = BollingerBands(symbol, prices)
                        bands = bb.calculate()
                        arr = {
                            "t": [],
                            "middle_band": [],
                            "upper_band": [],
                            "lower_band": [],
                            "band_width": [],
                        }
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            bb_sub = BollingerBands(symbol, sub_prices)
                            bands_sub = bb_sub.calculate()
                            width_sub = bb_sub.get_band_width()
                            arr["t"].append(ts)
                            arr["middle_band"].append(
                                bands_sub["middle_band"][-1]
                                if bands_sub["middle_band"]
                                else None
                            )
                            arr["upper_band"].append(
                                bands_sub["upper_band"][-1]
                                if bands_sub["upper_band"]
                                else None
                            )
                            arr["lower_band"].append(
                                bands_sub["lower_band"][-1]
                                if bands_sub["lower_band"]
                                else None
                            )
                            arr["band_width"].append(
                                width_sub[-1] if width_sub else None
                            )
                    elif ind == "cci":
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            cci = CommodityChannelIndex(symbol, sub_prices)
                            vals = cci.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "adx":
                        arr = {"t": [], "a": [], "p": [], "m": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            adx = AverageDirectionalIndex(symbol, sub_prices)
                            vals = adx.calculate()
                            arr["t"].append(ts)
                            arr["a"].append(vals["adx"][-1] if vals["adx"] else None)
                            arr["p"].append(
                                vals["plus_di"][-1] if vals["plus_di"] else None
                            )
                            arr["m"].append(
                                vals["minus_di"][-1] if vals["minus_di"] else None
                            )
                    elif ind == "wpr":
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            wpr = WilliamsPercentR(symbol, sub_prices)
                            vals = wpr.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "roc":
                        roc = RateOfChange(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = roc.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "ultosc":
                        ultosc = UltimateOscillator(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = ultosc.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "sar":
                        sar = ParabolicSAR(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = sar.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "obv":
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            obv_sub = OnBalanceVolume(symbol, sub_prices)
                            vals = obv_sub.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(
                                vals[-1] if vals and len(vals) > 0 else None
                            )
                    elif ind == "ao":
                        ao = AwesomeOscillator(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = ao.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "adi":
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            adi_sub = AccumulationDistributionIndex(symbol, sub_prices)
                            vals = adi_sub.calculate()
                            adi_list = vals.get("adi")
                            arr["t"].append(ts)
                            arr["v"].append(
                                adi_list[-1] if adi_list and len(adi_list) > 0 else None
                            )
                    elif ind == "momentum":
                        mom = Momentum(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = mom.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "bearpower":
                        bp = BearPower(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = bp.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "stochastic":
                        stoch = StochasticOscillator(symbol, prices)
                        arr = {"t": [], "k_values": [], "d_values": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = stoch.calculate()
                            arr["t"].append(ts)
                            arr["k_values"].append(
                                vals["k_values"][-1] if vals["k_values"] else None
                            )
                            arr["d_values"].append(
                                vals["d_values"][-1] if vals["d_values"] else None
                            )
                    elif ind == "stochrsi":
                        stochrsi = StochasticRSI(symbol, prices)
                        arr = {"t": [], "fastk": [], "fastd": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = stochrsi.calculate()
                            arr["t"].append(ts)
                            arr["fastk"].append(
                                vals["k_values"][-1] if vals["k_values"] else None
                            )
                            arr["fastd"].append(
                                vals["d_values"][-1] if vals["d_values"] else None
                            )
                    elif ind == "tsf":
                        tsf = TimeSeriesForecast(symbol, prices)
                        arr = {"t": [], "v": []}
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = tsf.calculate()
                            arr["t"].append(ts)
                            arr["v"].append(vals[-1] if vals else None)
                    elif ind == "pp":
                        pp = PivotPoints(symbol, prices)
                        arr = {
                            "t": [],
                            "pivot": [],
                            "r1": [],
                            "r2": [],
                            "r3": [],
                            "s1": [],
                            "s2": [],
                            "s3": [],
                        }
                        for ts in last5_fridays:
                            sub_prices = get_prices_up_to(prices, ts)
                            vals = pp.calculate()
                            arr["t"].append(ts)
                            arr["pivot"].append(vals.get("PP"))
                            arr["r1"].append(vals.get("R1"))
                            arr["r2"].append(vals.get("R2"))
                            arr["r3"].append(vals.get("R3"))
                            arr["s1"].append(vals.get("S1"))
                            arr["s2"].append(vals.get("S2"))
                            arr["s3"].append(vals.get("S3"))
                    elif ind == "ichimoku":
                        arr = {
                            "t": [],
                            "tk": [],
                            "kj": [],
                            "sa": [],
                            "sb": [],
                            "cs": [],
                        }
                        if last5_fridays is not None and prices is not None:
                            for ts in last5_fridays:
                                sub_prices = get_prices_up_to(prices, ts)
                                ichimoku = Ichimoku(symbol, sub_prices)
                                vals = ichimoku.calculate()
                                arr["t"].append(ts)
                                arr["tk"].append(
                                    vals["tenkan_sen"][-1]
                                    if vals["tenkan_sen"]
                                    else None
                                )
                                arr["kj"].append(
                                    vals["kijun_sen"][-1] if vals["kijun_sen"] else None
                                )
                                arr["sa"].append(
                                    vals["senkou_span_a"][-1]
                                    if vals["senkou_span_a"]
                                    else None
                                )
                                arr["sb"].append(
                                    vals["senkou_span_b"][-1]
                                    if vals["senkou_span_b"]
                                    else None
                                )
                                arr["cs"].append(
                                    vals["chikou_span"][-1]
                                    if vals["chikou_span"]
                                    else None
                                )

                    if arr:
                        # Pass last5_fridays for ichimoku
                        if ind == "ichimoku":
                            save_indicator_span(
                                symbol, ind, arr, prices, last5_fridays=last5_fridays
                            )
                        else:
                            save_indicator_span(symbol, ind, arr, prices)

                except Exception as e:
                    logging.warning(
                        f"Could not generate compact result for {symbol} {ind}: {e}"
                    )

            # Save history prices
            save_history_prices(symbol, days=100)

        except Exception as e:
            logging.error(
                msg=f"Failed to analyze {symbol}: {e}", exc_info=True, stack_info=True
            )


def export_to_json(file_path: str, data):
    with open(file=file_path, mode="w", encoding="utf-8") as f:
        if is_deploy:
            json.dump(data, f, ensure_ascii=False, separators=(",", ":"))
        else:
            json.dump(data, f, ensure_ascii=False, indent=2)

    logging.info(f"Saved analysis data to{file_path}")


def copy_site_files_to_repo(
    src_dir: str, dest_dir: str, prediction_js_file: str
) -> None:
    """
    Copy generated site files (index.html, script.js, styles.css, prediction JS) from src_dir to dest_dir.

    Args:
        src_dir (str): Source directory containing generated files.
        dest_dir (str): Destination directory (repo) to copy files to.
        prediction_js_file (str): Path to the generated prediction JS file.
    """
    files_to_copy = [
        os.path.join(src_dir, "index.html"),
        os.path.join(src_dir, "script.js"),
        os.path.join(src_dir, "styles.css"),
        prediction_js_file,
    ]
    if not os.path.exists(dest_dir):
        try:
            os.makedirs(dest_dir, exist_ok=True)
            logging.info(f"Created destination directory: {dest_dir}")
        except Exception as e:
            logging.error(f"Failed to create destination directory {dest_dir}: {e}")
            raise
    for src_path in files_to_copy:
        dest_path = os.path.join(dest_dir, os.path.basename(src_path))
        try:
            shutil.copy2(src_path, dest_path)
            logging.info(f"Copied {src_path} to {dest_path}")
        except Exception as e:
            logging.error(f"Failed to copy {src_path} to {dest_path}: {e}")
            raise


def git_commit_and_push(repo_dir: str, commit_message: str) -> None:
    """
    Run git pull, add, commit, and push in the specified repo directory.

    Args:
        repo_dir (str): Path to the git repository directory.
        commit_message (str): Commit message for the changes.
    """
    try:
        # Pull latest changes from main branch
        subprocess.run(["git", "pull", "origin", "main"], cwd=repo_dir, check=True)
        logging.info(f"Git pull completed in {repo_dir}")
        # Add all changes
        subprocess.run(["git", "add", "."], cwd=repo_dir, check=True)
        logging.info(f"Git add completed in {repo_dir}")
        # Commit changes
        subprocess.run(
            ["git", "commit", "-m", commit_message], cwd=repo_dir, check=True
        )
        logging.info(f"Git commit completed in {repo_dir}")
        # Push changes
        subprocess.run(["git", "push"], cwd=repo_dir, check=True)
        logging.info("Git push completed.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Git operation failed in {repo_dir}: {e}")
        raise


def analyze_stock(symbol: str, end_date: datetime | None) -> dict[str, Any]:
    data_reader = DataReader(symbol=symbol)
    prices = data_reader.get_daily_prices(days=365, end_date=end_date)

    # Indicator calculations use daily prices
    indicators = calculate_indicators(symbol, prices)
    indicator_signals = indicators.get("enhanced_signals", [])
    zones = format_zones(
        indicators.get("buy_zones", []),
        indicators.get("stop_loss_zones", []),
        indicators.get("take_profit_zones", []),
        indicators.get("current_price", 0),
    )
    risk_rewards = risk_reward_processing(
        indicators.get("risk_reward_ratios", []), indicators.get("current_price", 0)
    )
    technical_summary = summary_processing(
        symbol, prices, indicators, indicators.get("volume_spike", False)
    )
    return {
        "symbol": symbol,
        "current_price": indicators.get("current_price"),
        "trend_change": indicators.get("trend_change"),
        "trend_change_percent": indicators.get("trend_change_percent"),
        "trend_direction": indicators.get("trend_direction"),
        "trend_strength": indicators.get("trend_strength"),
        "trend_confidence": indicators.get("trend_confidence"),
        "market_condition": indicators.get("market_condition"),
        "buy_zones": zones["buy_zones"],
        "stop_loss_zones": zones["stop_loss_zones"],
        "take_profit_zones": zones["take_profit_zones"],
        "risk_reward_ratios": risk_rewards,
        "recommendation": indicators.get("recommendation"),
        "analysis_date": datetime.now().strftime("%Y-%m-%d"),
        "last_trading_date": indicators.get("last_trading_date"),
        "technical_summary": technical_summary,
        "ma_signals": indicators.get("ma_signals"),
        "indicator_signals": indicator_signals,
        "indicators": indicators.get("indicator_arrays", {}),
    }


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments for the stock analyzer script.

    Returns:
        argparse.Namespace: Parsed arguments namespace.
    """
    parser = argparse.ArgumentParser(
        description="Stock analysis and site generation workflow."
    )
    parser.add_argument(
        "--deploy",
        action="store_true",
        help="If set, perform post-processing: move files and push to GitHub.",
    )
    parser.add_argument(
        "--date",
        type=str,
        default=None,
        help="Ngày phân tích (định dạng YYYY-MM-DD, mặc định là ngày hiện tại)",
    )
    parser.add_argument(
        "--weekly",
        action="store_true",
        help="If set, aggregate daily prices into weekly bars ending on Friday for indicator calculation.",
    )
    return parser.parse_args()


def save_stock_current_info(analysis: dict[str, Any]):
    symbol = analysis.get("symbol")

    stock_data = {
        "s": analysis.get("symbol"),
        "ad": analysis.get("analysis_date"),
        "ltd": analysis.get("last_trading_date"),
        "p": {
            "c": analysis.get("current_price"),
            "cv": analysis.get("trend_change"),
            "cp": analysis.get("trend_change_percent"),
        },
        "t": {
            "d": analysis.get("trend_direction"),
            "s": analysis.get("trend_strength"),
            "c": analysis.get("trend_confidence"),
        },
        "mc": analysis.get("market_condition"),
        "r": {
            "r": analysis.get("recommendation"),  # recommendation
            "s": analysis.get("technical_summary"),  # technical summary
        },
        "bz": [map_zone(z) for z in analysis.get("buy_zones", [])],
        "slz": [map_zone(z) for z in analysis.get("stop_loss_zones", [])],
        "tpz": [map_zone(z) for z in analysis.get("take_profit_zones", [])],
        "rr": [map_rr(rr) for rr in analysis.get("risk_reward_ratios", [])],
        "ma": [map_ma(ma) for ma in analysis.get("ma_signals", [])],
        "ti": [map_ti(ti) for ti in analysis.get("indicator_signals", [])],
    }

    # Save to web2/public/analysis
    export_to_json(
        os.path.join(Utils.get_web2_dir(), "public", "analysis", f"{symbol}.json"),
        stock_data,
    )

    # Save to web_test/analysis
    export_to_json(
        os.path.join(Utils.get_web_test_dir(), "analysis", f"{symbol}.json"), stock_data
    )


# Helper to map zones (buy, stop loss, take profit)
def map_zone(z: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "p": z.get("price"),  # price
        "c": z.get("confidence"),  # confidence
        "r": z.get("reason"),  # reason
    }


# Helper to map risk/reward
def map_rr(rr: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "bp": rr.get("buy_price"),  # buy price
        "slp": rr.get("stop_loss_price"),  # stop loss price
        "tp": rr.get("take_profit_price"),  # take profit price
        "r": rr.get("ratio"),  # ratio
        "q": rr.get("quality"),  # quality
    }


# Helper to map MA signals
def map_ma(ma: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "p": ma.get("period"),  # period
        "t": ma.get("type"),  # type
        "v": ma.get("value"),  # value
        "s": ma.get("signal"),  # signal
    }


# Helper to map technical indicators
def map_ti(ti: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "n": ti.get("name"),  # name
        "v": ti.get("value"),  # value
        "s": ti.get("signal"),  # signal
        "a": ti.get("action"),  # action
        "d": ti.get("description"),  # description
        "i": ti.get("info"),  # info
    }


def save_indicator_span(
    symbol: str,
    indicator_name: str,
    values: dict[str, Any],
    prices: list[Any] | None,
    last5_fridays: list[int] = None,
):
    """
    Save indicator values to JSON files for web and test analysis.

    (a) Indicator values are calculated for each relevant date (e.g., last 5 Fridays),
        using only price data up to and including that date. This ensures that each
        exported value is aligned with its timestamp and that no future data is used
        (no lookahead bias).
    (b) If there is missing or insufficient data for a calculation (e.g., not enough
        price history for a moving average), the exported value for that date is None.
    (c) All exported arrays (timestamps and indicator values) are aligned by index,
        so that each value corresponds to the correct date.
    """

    arr = None

    if indicator_name == "ma":
        mapped = {
            "t": values["t"],
            "s5": values["sma5"],
            "e5": values["ema5"],
            "s10": values["sma10"],
            "e10": values["ema10"],
            "s20": values["sma20"],
            "e20": values["ema20"],
            "s50": values["sma50"],
            "e50": values["ema50"],
            "s100": values["sma100"],
            "e100": values["ema100"],
            "s200": values["sma200"],
            "e200": values["ema200"],
        }
    elif indicator_name == "macd":
        mapped = {
            "t": values["t"],
            "l": values["macd_line"],
            "s": values["signal_line"],
            "h": values["histogram"],
        }
    elif (
        indicator_name == "rsi"
        or indicator_name == "rsw"
        or indicator_name == "cci"
        or indicator_name == "roc"
        or indicator_name == "ultosc"
        or indicator_name == "sar"
        or indicator_name == "obv"
        or indicator_name == "ao"
        or indicator_name == "adi"
        or indicator_name == "momentum"
        or indicator_name == "bearpower"
        or indicator_name == "tsf"
    ):
        mapped = {"t": values["t"], "v": values["v"]}
    elif indicator_name == "adx":
        mapped = {
            "t": values["t"],
            "a": values["adx"],
            "p": values["plus_di"],
            "m": values["minus_di"],
        }
    elif indicator_name == "wpr":
        mapped = {"t": values["t"], "v": values["v"]}
    elif indicator_name == "stochastic":
        mapped = {
            "t": values["t"],
            "k": values["k_values"],
            "d": values["d_values"],
        }
    elif indicator_name == "stochrsi":
        mapped = {
            "t": values["t"],
            "k": values["fastk"],
            "d": values["fastd"],
        }
    elif indicator_name == "bb":
        mapped = {
            "t": values["t"],
            "m": values["middle_band"],
            "u": values["upper_band"],
            "l": values["lower_band"],
            "w": values["band_width"],
        }
    elif indicator_name == "pp":
        if prices is None:
            logging.error(
                f"'prices' argument is required for indicator 'pp' (pivot points) but was not provided."
            )
            return
        pp = PivotPoints(symbol, prices)
        vals = pp.calculate()
        key_map = {
            "PP": "pivot",
            "R1": "r1",
            "R2": "r2",
            "R3": "r3",
            "S1": "s1",
            "S2": "s2",
            "S3": "s3",
        }
        mapped_vals = {v: vals.get(k) for k, v in key_map.items()}
        required_keys = list(key_map.values())

        if all(k in mapped_vals and mapped_vals[k] is not None for k in required_keys):
            arr = {
                "t": [p.timestamp for p in prices][-5:],
                "pivot": [mapped_vals["pivot"]] * 5,
                "r1": [mapped_vals["r1"]] * 5,
                "r2": [mapped_vals["r2"]] * 5,
                "r3": [mapped_vals["r3"]] * 5,
                "s1": [mapped_vals["s1"]] * 5,
                "s2": [mapped_vals["s2"]] * 5,
                "s3": [mapped_vals["s3"]] * 5,
            }
            # Save the mapped arr directly
            mapped = arr
        else:
            missing = [
                k
                for k in required_keys
                if k not in mapped_vals or mapped_vals[k] is None
            ]
            logging.warning(
                f"PivotPoints calculation for {symbol} missing or None keys: {missing}"
            )
            return
    elif indicator_name == "ichimoku":
        arr = {"t": [], "tk": [], "kj": [], "sa": [], "sb": [], "cs": []}
        if last5_fridays is not None and prices is not None:
            for ts in last5_fridays:
                sub_prices = get_prices_up_to(prices, ts)
                ichimoku = Ichimoku(symbol, sub_prices)
                vals = ichimoku.calculate()
                arr["t"].append(ts)
                arr["tk"].append(vals["tenkan_sen"][-1] if vals["tenkan_sen"] else None)
                arr["kj"].append(vals["kijun_sen"][-1] if vals["kijun_sen"] else None)
                arr["sa"].append(
                    vals["senkou_span_a"][-1] if vals["senkou_span_a"] else None
                )
                arr["sb"].append(
                    vals["senkou_span_b"][-1] if vals["senkou_span_b"] else None
                )
                arr["cs"].append(
                    vals["chikou_span"][-1] if vals["chikou_span"] else None
                )

    if arr:
        # Pass last5_fridays for ichimoku
        if indicator_name == "ichimoku":
            save_indicator_span(
                symbol, indicator_name, arr, prices, last5_fridays=last5_fridays
            )
        else:
            save_indicator_span(symbol, indicator_name, arr, prices)

    export_to_json(
        os.path.join(
            Utils.get_web2_dir(),
            "public",
            "analysis",
            f"{symbol}_{indicator_name}.json",
        ),
        mapped,
    )

    # Also write to web_test/analysis
    export_to_json(
        os.path.join(
            Utils.get_web_test_dir(), "analysis", f"{symbol}_{indicator_name}.json"
        ),
        mapped,
    )


def save_history_prices(symbol: str, days: int = 30) -> None:
    data_reader = DataReader(symbol)
    prices = data_reader.get_daily_prices(days=days)

    # Always sort by timestamp in ascending order before exporting to JSON
    prices.sort(key=lambda x: x.timestamp)
    
    t = [p.timestamp for p in prices]
    o = [p.open_price for p in prices]
    h = [p.highest_price for p in prices]
    l = [p.lowest_price for p in prices]
    c = [p.close_price for p in prices]
    v = [p.match_volume for p in prices]
    history = {"t": t, "o": o, "h": h, "l": l, "c": c, "v": v}

    # Save to web2/public/analysis
    export_to_json(
        os.path.join(
            Utils.get_web2_dir(), "public", "analysis", f"{symbol}_prices.json"
        ),
        history,
    )

    # Also save to web_test/analysis
    export_to_json(
        os.path.join(Utils.get_web_test_dir(), "analysis", f"{symbol}_prices.json"),
        history,
    )


if __name__ == "__main__":
    args = parse_args()
    logging.debug(f"Args: {args}")
    is_deploy = args.deploy

    main(
        analysis_symbols=["HPG"] if not is_deploy else None,
        analysis_date=args.date if args.date else datetime.now().strftime("%Y-%m-%d"),
    )
