<!DOCTYPE html>
<html lang="vi">
<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CVQLJP6C40"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-CVQLJP6C40');
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockPal - Bid & Ask Price</title>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script src="https://cdn.jsdelivr.net/npm/handlebars@latest/dist/handlebars.js"></script>
    <script src="prediction-2025-05-22.js" defer></script>
    <script src="script.js" defer></script>
</head>
<body class="flex flex-col h-screen bg-slate-50">
    <nav class="bg-gray-800 text-white p-2">
        <div class="flex items-center justify-between">
            <div class="flex items-center justify-center sm:items-stretch sm:justify-start">
                <div class="flex shrink-0 items-center">
                    <span class="material-symbols-outlined text-3xl">analytics</span>
                </div>
                <div class="ml-6 max-sm:hidden">
                    <h1 class="text-xl font-bold">Xu hướng</h1>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <div class="relative">
                    <span class="material-symbols-outlined absolute left-2 top-1/2 -translate-y-1/2 text-gray-400">search</span>
                    <input type="text" id="stockSearch" placeholder="Tìm mã cổ phiếu..." class="pl-8 pr-4 py-1 rounded bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="hidden" id="viewMode" value="grid">
                </div>
            </div>
        </div>
    </nav>

    <div class="flex flex-1 overflow-hidden">
        <div class="flex-1 flex flex-col overflow-hidden bg-slate-50">
            <!-- Tab Bar -->
            <div class="flex items-center justify-between gap-2 p-2 bg-white border-b border-gray-200">
                <div class="flex flex-wrap items-center gap-2" id="tabList">
                    <!-- Stock List Tab (Fixed) -->
                    <button class="tab-button active px-4 py-2 rounded-full bg-blue-100 text-blue-700 font-medium" data-tab="stockList">
                        Danh sách
                    </button>
                    <!-- Dynamic tabs will be added here -->
                </div>
            </div>

            <!-- Tab Content -->
            <div class="flex-1 overflow-y-auto" id="tabContent">
                <!-- Stock List Content -->
                <div class="tab-content active" id="stockList">
                    <div class="flex items-center gap-2 p-2 bg-white border-b border-gray-200">
                        <button id="gridViewBtn" class="flex items-center justify-center p-2 rounded hover:bg-gray-100 active:bg-gray-200" title="Dạng lưới">
                            <span class="material-symbols-outlined">grid_view</span>
                        </button>
                        <button id="tableViewBtn" class="flex items-center justify-center p-2 rounded hover:bg-gray-100 active:bg-gray-200" title="Dạng bảng">
                            <span class="material-symbols-outlined">table_rows</span>
                        </button>
                        <div id="lastUpdated" class="text-sm text-gray-500 ml-auto"></div>
                    </div>
                    <div id="gridView" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 p-2">
                        <!-- Grid view content will be loaded here -->
                    </div>
                    <div id="tableView" class="hidden p-2">
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-center"></th>
                                        <th class="px-4 py-2">Mã CK</th>
                                        <th class="px-4 py-2 text-right">Giá</th>
                                        <th class="px-4 py-2">Xu hướng</th>
                                        <th class="px-4 py-2">Độ mạnh</th>
                                        <th class="px-4 py-2">Độ tin cậy</th>
                                        <th class="px-4 py-2">MA</th>
                                        <th class="px-4 py-2">Kỹ thuật</th>
                                        <th class="px-4 py-2" style="min-width: 200px; max-width: 250px;">Khuyến nghị</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Table rows will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- Dynamic tab contents will be added here -->
            </div>
        </div>
    </div>

    <!-- Handlebars Templates -->
    <script id="card-template" type="text/x-handlebars-template">
        <div class="stock-card bg-white rounded-lg border border-slate-200 p-2 shadow-sm hover:shadow-md hover:-translate-y-1 transition-all" data-symbol="{{symbol}}">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <img src="./assets/logos/{{symbol}}.svg" alt="{{symbol}}" class="w-10 h-10 mr-3">
                    <div class="text-xl font-semibold">{{symbol}}</div>
                </div>
                <div class="text-xl font-bold">{{current_price}}</div>
            </div>

            <div class="flex justify-between items-center mb-3 px-2">
                <div class="flex items-center gap-1" title="Xu hướng: {{trend_direction}}">
                    <span class="material-symbols-outlined text-2xl {{trendClass trend_direction}}">
                        {{trendIcon trend_direction}}
                    </span>
                    <span class="text-sm {{trendClass trend_direction}}">{{trend_direction}}</span>
                </div>
                <div class="flex items-center gap-1" title="Độ mạnh: {{trend_strength}}">
                    <span class="material-symbols-outlined text-2xl text-blue-500">
                        {{strengthIcon trend_strength}}
                    </span>
                    <span class="text-sm text-blue-500">{{trend_strength}}</span>
                </div>
                <div class="flex items-center gap-1" title="Độ tin cậy: {{trend_confidence}}">
                    <span class="material-symbols-outlined text-2xl text-yellow-500">
                        {{confidenceIcon trend_confidence}}
                    </span>
                    <span class="text-sm text-yellow-500">{{trend_confidence}}</span>
                </div>
            </div>

            <div class="p-2 bg-slate-50 rounded mb-3" style="overflow-wrap: break-word;">
                <div class="font-medium mb-1">Khuyến nghị:</div>
                <div class="text-sm recommendation-text" style="white-space: normal; word-break: break-word;">{{nl2br recommendation}}</div>
            </div>
            
            {{#if ma_signals}}
            <div class="mb-3">
                <div class="text-sm font-medium mb-1">Tổng hợp tín hiệu:</div>
                {{#with (getIndicatorSummary ma_signals indicator_signals)}}
                <div class="flex flex-col gap-1 p-2 bg-gray-50 rounded-md">
                    <div class="flex justify-between items-center text-xs text-gray-600">
                        <span>Đường MA ({{ma_count}}):</span>
                        <div class="flex gap-1">
                            <span class="px-1.5 rounded bg-green-100 text-green-800">{{ma_buy}}</span>
                            <span class="px-1.5 rounded bg-red-100 text-red-800">{{ma_sell}}</span>
                            <span class="px-1.5 rounded bg-gray-100 text-gray-800">{{ma_neutral}}</span>
                        </div>
                    </div>
                    {{#if ../indicator_signals}}
                    <div class="flex justify-between items-center text-xs text-gray-600">
                        <span>Chỉ báo kỹ thuật ({{indicator_count}}):</span>
                        <div class="flex gap-1">
                            <span class="px-1.5 rounded bg-green-100 text-green-800">{{tech_buy}}</span>
                            <span class="px-1.5 rounded bg-red-100 text-red-800">{{tech_sell}}</span>
                            <span class="px-1.5 rounded bg-gray-100 text-gray-800">{{tech_neutral}}</span>
                        </div>
                    </div>
                    {{/if}}
                    <div class="flex justify-between items-center text-xs font-medium mt-1 pt-1 border-t border-gray-200">
                        <span>Tổng hợp ({{total_count}}):</span>
                        <div class="flex gap-1">
                            <span class="px-1.5 rounded bg-green-100 text-green-800">{{total_buy}} Mua</span>
                            <span class="px-1.5 rounded bg-red-100 text-red-800">{{total_sell}} Bán</span>
                            <span class="px-1.5 rounded bg-gray-100 text-gray-800">{{total_neutral}} TT</span>
                        </div>
                    </div>
                </div>
                {{/with}}
            </div>
            {{/if}}
        </div>
    </script>
    <script id="table-row-template" type="text/x-handlebars-template">
        <tr data-symbol="{{symbol}}" class="hover:bg-gray-50">
            <td class="px-4 py-2 text-center">
                <img src="./assets/logos/{{symbol}}.svg" alt="{{symbol}}" class="w-8 h-8 mx-auto">
            </td>
            <td class="px-4 py-2 text-sm font-medium">{{symbol}}</td>
            <td class="px-4 py-2 text-sm text-right font-semibold">{{current_price}}</td>
            <td class="px-4 py-2 text-sm {{trendClass trend_direction}}">
                <div class="flex items-center gap-1">
                    <span class="material-symbols-outlined text-base">
                        {{trendIcon trend_direction}}
                    </span>
                    <span>{{trend_direction}}</span>
                </div>
            </td>
            <td class="px-4 py-2 text-sm text-blue-500">
                <div class="flex items-center gap-1">
                    <span class="material-symbols-outlined text-base">
                        {{strengthIcon trend_strength}}
                    </span>
                    <span>{{trend_strength}}</span>
                </div>
            </td>
            <td class="px-4 py-2 text-sm text-yellow-500 text-center">
                <div class="flex items-center justify-center gap-1">
                    <span class="material-symbols-outlined text-base">
                        {{confidenceIcon trend_confidence}}
                    </span>
                    <span>{{trend_confidence}}</span>
                </div>
            </td>
            <td class="px-4 py-2 text-sm">
                {{#if ma_signals}}
                <div class="flex items-center gap-1 justify-center">
                    {{#with (getIndicatorSummary ma_signals indicator_signals)}}
                    <span class="w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-800 font-medium">{{ma_count}}</span>
                    <div class="flex">
                        <span class="px-1 rounded-l bg-green-100 text-green-800">{{ma_buy}}</span>
                        <span class="px-1 bg-red-100 text-red-800">{{ma_sell}}</span>
                        <span class="px-1 rounded-r bg-gray-100 text-gray-800">{{ma_neutral}}</span>
                    </div>
                    {{/with}}
                </div>
                {{else}}
                <span class="text-xs text-gray-500">Không có dữ liệu</span>
                {{/if}}
            </td>
            <td class="px-4 py-2 text-sm">
                {{#if indicator_signals}}
                <div class="flex items-center gap-1 justify-center">
                    {{#with (getIndicatorSummary ma_signals indicator_signals)}}
                    <span class="w-6 h-6 flex items-center justify-center rounded-full bg-purple-100 text-purple-800 font-medium">{{indicator_count}}</span>
                    <div class="flex">
                        <span class="px-1 rounded-l bg-green-100 text-green-800">{{tech_buy}}</span>
                        <span class="px-1 bg-red-100 text-red-800">{{tech_sell}}</span>
                        <span class="px-1 rounded-r bg-gray-100 text-gray-800">{{tech_neutral}}</span>
                    </div>
                    {{/with}}
                </div>
                {{else}}
                <span class="text-xs text-gray-500">Không có dữ liệu</span>
                {{/if}}
            </td>
            <td class="px-4 py-2 text-sm recommendation-text" style="min-width: 200px; max-width: 250px; white-space: normal;">{{nl2br recommendation}}</td>
        </tr>
    </script>

    <script id="ma-signals-template" type="text/x-handlebars-template">
        <div class="bg-white rounded-lg border border-slate-200 p-2 shadow-sm">
            <h3 class="text-lg font-semibold mb-4">Phân tích đường MA</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full border border-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left border-b border-gray-200">Chu kỳ</th>
                            <th class="px-4 py-2 text-right border-b border-gray-200">SMA</th>
                            <th class="px-4 py-2 text-center border-b border-gray-200">Tín hiệu SMA</th>
                            <th class="px-4 py-2 text-right border-b border-gray-200">EMA</th>
                            <th class="px-4 py-2 text-center border-b border-gray-200">Tín hiệu EMA</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each (groupMAByPeriod ma_signals)}}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-2 border-b border-gray-200 font-medium">{{period}}</td>
                            
                            <!-- SMA value and signal -->
                            <td class="px-4 py-2 text-right border-b border-gray-200 font-medium">
                                {{#if sma.value}}{{sma.value}}{{else}}-{{/if}}
                            </td>
                            <td class="px-4 py-2 text-center border-b border-gray-200">
                                {{#if sma.signal}}
                                <span class="px-3 py-1 rounded-full text-sm font-medium {{#if (eq sma.signal "Mua")}}bg-green-100 text-green-800{{else if (eq sma.signal "Bán")}}bg-red-100 text-red-800{{else}}bg-gray-100 text-gray-800{{/if}}">
                                    {{sma.signal}}
                                </span>
                                {{else}}
                                -
                                {{/if}}
                            </td>
                            
                            <!-- EMA value and signal -->
                            <td class="px-4 py-2 text-right border-b border-gray-200 font-medium">
                                {{#if ema.value}}{{ema.value}}{{else}}-{{/if}}
                            </td>
                            <td class="px-4 py-2 text-center border-b border-gray-200">
                                {{#if ema.signal}}
                                <span class="px-3 py-1 rounded-full text-sm font-medium {{#if (eq ema.signal "Mua")}}bg-green-100 text-green-800{{else if (eq ema.signal "Bán")}}bg-red-100 text-red-800{{else}}bg-gray-100 text-gray-800{{/if}}">
                                    {{ema.signal}}
                                </span>
                                {{else}}
                                -
                                {{/if}}
                            </td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
            </div>
        </div>
    </script>
</body>
</html>
